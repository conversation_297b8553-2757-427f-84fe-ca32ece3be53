services:

  ts_ui:
    image: timeseries_studio
    networks:
      - ts_network
    build:
      context: .
      dockerfile: Dockerfile.dockerfile
    ports:
      - 8501:8501
 
  ts_db:
    image: questdb/questdb:8.1.0
    networks:
      - ts_network

    environment:
      QDB_ILP_ENABLED: "true"  # Enable ILP (optional, usually on by default)
      QDB_PG_ENABLED: "true"   # Enable PostgreSQL wire protocol (optional, usually on by default)
      QDB_HTTP_ENABLED: "true" # Enable HTTP API (optional, usually on by default)
    ports:
      - 9000:9000 # REST API and Web Console
      - 9009:9009 # InfluxDB Line Protocol
      - 8812:8812 # Postgres wire protocol
      - 9003:9003 # Min health server
    volumes:
      - questdb-data:/root/.questdb/db

  # ts_llm:
  #   image: vllm/vllm-openai:latest
  #   runtime: nvidia
  #   deploy:
  #     resources:
  #       reservations:
  #         devices:
  #           - driver: nvidia
  #             count: all
  #             capabilities: [gpu]
  #   environment:
  #     - HUGGING_FACE_HUB_TOKEN=${HUGGING_FACE_HUB_TOKEN}
  #   volumes:
  #     - ~/.cache/huggingface:/root/.cache/huggingface
  #   ports:
  #     - "8000:8000"
  #   ipc: host
  #   command: ["--model", "mistralai/Mistral-7B-v0.1"]

  ts_llm:
    image: ollama/ollama
    container_name: ollama
    restart: unless-stopped
    volumes:
      - ollama:/root/.ollama
    ports:
      - "11434:11434"
    #detach: true

volumes:
  questdb-data:
  ollama:
  
networks:
  ts_network:
    driver: bridge