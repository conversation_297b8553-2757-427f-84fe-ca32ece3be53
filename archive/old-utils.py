def expand_timestamps(self, df, freq=None, meas_vars=None, method='interpolate'):
    ''' Function to expand timestamps and address any missing values'''

    df['ds'] = pd.to_datetime(df['ds'])
    df.index = df['ds']
    
    # drop true duplicates and alert the user
    pre_rows = df.shape[0]
    df = df.drop_duplicates(subset=['unique_id', 'ds'])
    post_rows = df.shape[0]
    diff_rows = pre_rows - post_rows
    
    print(f'DROPPED {diff_rows} DUPLICATE ROWS OR ABOUT {np.round(diff_rows/pre_rows, 3)*100}% ...')
    
    # determine timeseries frequency
    #TODO: fix this. pd.infer_freq doesn't seem to work if the freq is already None in the DatetimeIndex
    if freq is None:
        try:
            freq = pd.infer_freq(df.index)
            print(f'IDENTIFIED FREQUENCY: {freq}')
        except:
            print(f'FAILED TO INFER FREQUENCY...')
    
    resampled = df.groupby('unique_id').resample(freq).mean()
                    
    for var in resampled.columns:
        if var in meas_vars:
            if method == 'interpolate':
                resampled[var] = resampled[var].interpolate()
            elif method == "forward_fill": 
                resampled[var] = resampled[var].pad()
        elif var not in ['ds', 'unique_id']:
            resampled[var] = resampled[var].pad()
            
    if 'ds' in resampled.columns:
        resampled.drop(columns=['ds'], inplace=True)        
    resampled.reset_index(inplace=True)
    
    return resampled

def decompose(self, df, y, season_length=[24, 24*7]):
    ''' Function to decompose timeseries using MSTL to account for potential multiple seasonalities'''
    ''' Reference: https://arxiv.org/abs/2107.13462'''

    models = [MSTL(
        season_length=season_length,
        trend_forecaster=AutoARIMA()
    )]

    sf = StatsForecast(
        models=models, # model used to fit each time series 
        freq='H', # frequency of the data
    )

    sf = sf.fit(df=df[['ds', 'unique_id', y]])
    
    decomposed = sf.fitted_[0, 0].model_
    decomposed.rename(columns={'data': y}, inplace=True)
    return decomposed

def plot_decompose(self, df, n_history = 24 * 28):
    ''' Function to plot a decomposed timeseries '''
    df.tail(n_history).plot(subplots=True, grid=True)
    plt.tight_layout()
    plt.show()
    
def parse_monash_df(file):
    ''' Function to Parse a Locally Extracted and Downloaded File'''
    
    loaded_data, frequency, forecast_horizon, contain_missing_values, contain_equal_length = convert_tsf_to_dataframe(file)

    print(f'PARSING FILE: {file}...')
    print(f"IDENTIFIED FREQUENCY: {frequency}...")
    print(f"IDENTIFIED FORECAST HORIZON: {forecast_horizon}")

    parsed_df = pd.DataFrame()

    #freq = frequency
    if frequency == 'yearly':
        freq = 'YS' #year start
    elif frequency == 'quarterly':
        freq = 'QS' #quarter start
    elif frequency == 'monthly':
        freq = 'MS'
    elif frequency == 'daily':
        freq = 'D'
    elif frequency == 'hourly':
        freq = 'H'


    for index, row in tqdm(loaded_data.iterrows()):
        
        name = row.series_name

        try:
            values = row.series_value.tolist()
            length = len(values)
            start = row.start_timestamp
            ds = pd.date_range(start, periods=length, freq=freq)
            series_df = pd.DataFrame({'unique_id':name,'ds':ds, 'values':values})
        except:
            print(f'FAILED PARSING TIMESERIES: {name}')
            series_df = pd.DataFrame()
            
        parsed_df = pd.concat([parsed_df, series_df], axis=0)
        
    return parsed_df

def convert_tsf_to_dataframe(full_file_path_and_name, replace_missing_vals_with="NaN", value_column_name="series_value"):
    '''
    Converts the contents in a .tsf file into a dataframe and returns it along with other meta-data of the dataset.
    
        Parameters:
            full_file_path_and_name (str): complete .tsf file path
            replace_missing_vals_with (str): a term to indicate the missing values in series in the returning dataframe
            value_column_name (str): Any name that is preferred to have as the name of the column containing series values in the returning dataframe
    
        Returns:
            data (pd.DataFrame): load data frame
            frequency (str): time series frequency
            horizon (int): time series forecasting horizon
            missing (bool): whether the dataset contains missing values
            equal (bool): whether the series have equal lengths
    
    '''
    
    col_names = []
    col_types = []
    all_data = {}
    line_count = 0
    frequency = None
    forecast_horizon = None
    contain_missing_values = None
    contain_equal_length = None
    found_data_tag = False
    found_data_section = False
    started_reading_data_section = False

    with open(full_file_path_and_name, "r", encoding="cp1252") as file:
        for line in file:
            # Strip white space from start/end of line
            line = line.strip()

            if line:
                if line.startswith("@"):  # Read meta-data
                    if not line.startswith("@data"):
                        line_content = line.split(" ")
                        if line.startswith("@attribute"):
                            if (
                                len(line_content) != 3
                            ):  # Attributes have both name and type
                                raise Exception("Invalid meta-data specification.")

                            col_names.append(line_content[1])
                            col_types.append(line_content[2])
                        else:
                            if (
                                len(line_content) != 2
                            ):  # Other meta-data have only values
                                raise Exception("Invalid meta-data specification.")

                            if line.startswith("@frequency"):
                                frequency = line_content[1]
                            elif line.startswith("@horizon"):
                                forecast_horizon = int(line_content[1])
                            elif line.startswith("@missing"):
                                contain_missing_values = bool(
                                    strtobool(line_content[1])
                                )
                            elif line.startswith("@equallength"):
                                contain_equal_length = bool(strtobool(line_content[1]))

                    else:
                        if len(col_names) == 0:
                            raise Exception(
                                "Missing attribute section. Attribute section must come before data."
                            )

                        found_data_tag = True
                elif not line.startswith("#"):
                    if len(col_names) == 0:
                        raise Exception(
                            "Missing attribute section. Attribute section must come before data."
                        )
                    elif not found_data_tag:
                        raise Exception("Missing @data tag.")
                    else:
                        if not started_reading_data_section:
                            started_reading_data_section = True
                            found_data_section = True
                            all_series = []

                            for col in col_names:
                                all_data[col] = []

                        full_info = line.split(":")

                        if len(full_info) != (len(col_names) + 1):
                            raise Exception("Missing attributes/values in series.")

                        series = full_info[len(full_info) - 1]
                        series = series.split(",")

                        if len(series) == 0:
                            raise Exception(
                                "A given series should contains a set of comma separated numeric values. At least one numeric value should be there in a series. Missing values should be indicated with ? symbol"
                            )

                        numeric_series = []

                        for val in series:
                            if val == "?":
                                numeric_series.append(replace_missing_vals_with)
                            else:
                                numeric_series.append(float(val))

                        if numeric_series.count(replace_missing_vals_with) == len(
                            numeric_series
                        ):
                            raise Exception(
                                "All series values are missing. A given series should contains a set of comma separated numeric values. At least one numeric value should be there in a series."
                            )

                        all_series.append(pd.Series(numeric_series).array)

                        for i in range(len(col_names)):
                            att_val = None
                            if col_types[i] == "numeric":
                                att_val = int(full_info[i])
                            elif col_types[i] == "string":
                                att_val = str(full_info[i])
                            elif col_types[i] == "date":
                                att_val = datetime.strptime(
                                    full_info[i], "%Y-%m-%d %H-%M-%S"
                                )
                            else:
                                raise Exception(
                                    "Invalid attribute type."
                                )  # Currently, the code supports only numeric, string and date types. Extend this as required.

                            if att_val is None:
                                raise Exception("Invalid attribute value.")
                            else:
                                all_data[col_names[i]].append(att_val)

                line_count = line_count + 1

        if line_count == 0:
            raise Exception("Empty file.")
        if len(col_names) == 0:
            raise Exception("Missing attribute section.")
        if not found_data_section:
            raise Exception("Missing series information under data section.")

        all_data[value_column_name] = all_series
        loaded_data = pd.DataFrame(all_data)

        return (
            loaded_data,
            frequency,
            forecast_horizon,
            contain_missing_values,
            contain_equal_length,
        )