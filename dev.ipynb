{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Make Database Connection"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from commandcast.data_manager import DataManager, DataManagerConfig\n", "\n", "db = DataManager(\n", "    host='localhost',\n", "    port=9000\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Ingest Data"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[32m2024-12-18 11:32:25.453\u001b[0m | \u001b[33m\u001b[1mWARNING \u001b[0m | \u001b[36mcommandcast.data_manager\u001b[0m:\u001b[36mcreate_dataset\u001b[0m:\u001b[36m163\u001b[0m - \u001b[33m\u001b[1mTimeseries and features table already exists. Aborting ingest.\u001b[0m\n"]}], "source": ["import pandas as pd\n", "\n", "df = pd.read_csv(\"data/m4.csv\")\n", "df.head()\n", "\n", "df['ds'] = pd.to_datetime(df['ds'])\n", "df.dtypes\n", "\n", "# ID Columns \n", "ds_col = 'ds'\n", "id_col = 'unique_id'\n", "\n", "# Measurement Columns\n", "measure_cols = list(df.drop(['ds', 'unique_id'], axis=1).columns)        \n", "\n", "# Configure Dataset in Database\n", "config = DataManagerConfig(**{\n", "    'dataset_name' : 'M4_HOURLY', # Overall name of the timeseries dataset\n", "    'feature_engineering': 'minimal',\n", "    'ds_col' : ds_col, # Timeseries column\n", "    'id_col' : id_col, # Identifier Unique to Timeseries,\n", "    'hierarchy': [],\n", "    'measure_cols': measure_cols\n", "})\n", "\n", "# Method to Create Tables and Ingest Data\n", "db.create_dataset(config=config, df=df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Explore how to create time series featues"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Feature Extraction: 100%|██████████| 25/25 [00:00<00:00, 33.63it/s]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>unique_id</th>\n", "      <th>time_begin</th>\n", "      <th>time_end</th>\n", "      <th>count</th>\n", "      <th>dataset_name</th>\n", "      <th>values__sum_values</th>\n", "      <th>values__median</th>\n", "      <th>values__mean</th>\n", "      <th>values__length</th>\n", "      <th>values__standard_deviation</th>\n", "      <th>values__variance</th>\n", "      <th>values__root_mean_square</th>\n", "      <th>values__maximum</th>\n", "      <th>values__absolute_maximum</th>\n", "      <th>values__minimum</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>T1</td>\n", "      <td>2015-07-01 12:00:00</td>\n", "      <td>2015-08-01 15:00:00</td>\n", "      <td>748</td>\n", "      <td>M4_HOURLY</td>\n", "      <td>478586.0</td>\n", "      <td>634.0</td>\n", "      <td>639.820856</td>\n", "      <td>748.0</td>\n", "      <td>156.533479</td>\n", "      <td>24502.729939</td>\n", "      <td>658.690714</td>\n", "      <td>926.0</td>\n", "      <td>926.0</td>\n", "      <td>349.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>T10</td>\n", "      <td>2015-07-01 12:00:00</td>\n", "      <td>2015-08-01 15:00:00</td>\n", "      <td>748</td>\n", "      <td>M4_HOURLY</td>\n", "      <td>331441.0</td>\n", "      <td>442.0</td>\n", "      <td>443.102941</td>\n", "      <td>748.0</td>\n", "      <td>35.451199</td>\n", "      <td>1256.787531</td>\n", "      <td>444.518846</td>\n", "      <td>517.0</td>\n", "      <td>517.0</td>\n", "      <td>336.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>T100</td>\n", "      <td>2015-07-01 12:00:00</td>\n", "      <td>2015-08-01 15:00:00</td>\n", "      <td>748</td>\n", "      <td>M4_HOURLY</td>\n", "      <td>753242.5</td>\n", "      <td>964.0</td>\n", "      <td>1007.008690</td>\n", "      <td>748.0</td>\n", "      <td>386.660351</td>\n", "      <td>149506.226863</td>\n", "      <td>1078.690284</td>\n", "      <td>1875.0</td>\n", "      <td>1875.0</td>\n", "      <td>351.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>T101</td>\n", "      <td>2015-07-01 12:00:00</td>\n", "      <td>2015-08-01 15:00:00</td>\n", "      <td>748</td>\n", "      <td>M4_HOURLY</td>\n", "      <td>1777609.0</td>\n", "      <td>2374.0</td>\n", "      <td>2376.482620</td>\n", "      <td>748.0</td>\n", "      <td>247.417708</td>\n", "      <td>61215.522425</td>\n", "      <td>2389.327346</td>\n", "      <td>2992.0</td>\n", "      <td>2992.0</td>\n", "      <td>1533.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>T102</td>\n", "      <td>2015-07-01 12:00:00</td>\n", "      <td>2015-08-01 15:00:00</td>\n", "      <td>748</td>\n", "      <td>M4_HOURLY</td>\n", "      <td>1305922.0</td>\n", "      <td>1739.0</td>\n", "      <td>1745.885027</td>\n", "      <td>748.0</td>\n", "      <td>425.193520</td>\n", "      <td>180789.529562</td>\n", "      <td>1796.915150</td>\n", "      <td>2540.0</td>\n", "      <td>2540.0</td>\n", "      <td>634.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  unique_id          time_begin            time_end  count dataset_name  \\\n", "0        T1 2015-07-01 12:00:00 2015-08-01 15:00:00    748    M4_HOURLY   \n", "1       T10 2015-07-01 12:00:00 2015-08-01 15:00:00    748    M4_HOURLY   \n", "2      T100 2015-07-01 12:00:00 2015-08-01 15:00:00    748    M4_HOURLY   \n", "3      T101 2015-07-01 12:00:00 2015-08-01 15:00:00    748    M4_HOURLY   \n", "4      T102 2015-07-01 12:00:00 2015-08-01 15:00:00    748    M4_HOURLY   \n", "\n", "   values__sum_values  values__median  values__mean  values__length  \\\n", "0            478586.0           634.0    639.820856           748.0   \n", "1            331441.0           442.0    443.102941           748.0   \n", "2            753242.5           964.0   1007.008690           748.0   \n", "3           1777609.0          2374.0   2376.482620           748.0   \n", "4           1305922.0          1739.0   1745.885027           748.0   \n", "\n", "   values__standard_deviation  values__variance  values__root_mean_square  \\\n", "0                  156.533479      24502.729939                658.690714   \n", "1                   35.451199       1256.787531                444.518846   \n", "2                  386.660351     149506.226863               1078.690284   \n", "3                  247.417708      61215.522425               2389.327346   \n", "4                  425.193520     180789.529562               1796.915150   \n", "\n", "   values__maximum  values__absolute_maximum  values__minimum  \n", "0            926.0                     926.0            349.0  \n", "1            517.0                     517.0            336.0  \n", "2           1875.0                    1875.0            351.0  \n", "3           2992.0                    2992.0           1533.0  \n", "4           2540.0                    2540.0            634.0  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["features = db.create_features(df, config=config)\n", "features.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Extract a time series from the database"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  unique_id dataset_name                     time_end  count  \\\n", "0        T2    M4_HOURLY  2015-08-01T15:00:00.000000Z    748   \n", "\n", "   values__sum_values  values__median  values__mean  values__length  \\\n", "0           1943707.0          2517.0    2598.53877           748.0   \n", "\n", "   values__standard_deviation  values__variance  values__root_mean_square  \\\n", "0                  655.316397     429439.580048                2679.89614   \n", "\n", "   values__maximum  values__absolute_maximum  values__minimum  \\\n", "0           4013.0                    4013.0           1466.0   \n", "\n", "                     timestamp  \n", "0  2015-07-01T12:00:00.000000Z  \n", "    unique_id  values                           ds\n", "0          T2  3124.0  2015-07-01T12:00:00.000000Z\n", "1          T2  2990.0  2015-07-01T13:00:00.000000Z\n", "2          T2  2862.0  2015-07-01T14:00:00.000000Z\n", "3          T2  2809.0  2015-07-01T15:00:00.000000Z\n", "4          T2  2544.0  2015-07-01T16:00:00.000000Z\n", "..        ...     ...                          ...\n", "743        T2  3558.0  2015-08-01T11:00:00.000000Z\n", "744        T2  3488.0  2015-08-01T12:00:00.000000Z\n", "745        T2  3224.0  2015-08-01T13:00:00.000000Z\n", "746        T2  3054.0  2015-08-01T14:00:00.000000Z\n", "747        T2  2918.0  2015-08-01T15:00:00.000000Z\n", "\n", "[748 rows x 3 columns]\n"]}], "source": ["id = 'T2'\n", "features = db.get_features(id, table_name = config['ft_table_name'])\n", "print(features)\n", "series = db.get_series(id, table_name = config['ts_table_name'])\n", "print(series)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "Date=%{x}<br>Values=%{y}<extra></extra>", "legendgroup": "", "line": {"color": "#636efa", "dash": "solid"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "", "orientation": "v", "showlegend": false, "type": "scatter", "x": ["2015-07-01T12:00:00.000000Z", "2015-07-01T13:00:00.000000Z", "2015-07-01T14:00:00.000000Z", "2015-07-01T15:00:00.000000Z", "2015-07-01T16:00:00.000000Z", "2015-07-01T17:00:00.000000Z", "2015-07-01T18:00:00.000000Z", "2015-07-01T19:00:00.000000Z", "2015-07-01T20:00:00.000000Z", "2015-07-01T21:00:00.000000Z", "2015-07-01T22:00:00.000000Z", "2015-07-01T23:00:00.000000Z", "2015-07-02T00:00:00.000000Z", "2015-07-02T01:00:00.000000Z", "2015-07-02T02:00:00.000000Z", "2015-07-02T03:00:00.000000Z", "2015-07-02T04:00:00.000000Z", "2015-07-02T05:00:00.000000Z", "2015-07-02T06:00:00.000000Z", "2015-07-02T07:00:00.000000Z", "2015-07-02T08:00:00.000000Z", "2015-07-02T09:00:00.000000Z", "2015-07-02T10:00:00.000000Z", "2015-07-02T11:00:00.000000Z", "2015-07-02T12:00:00.000000Z", "2015-07-02T13:00:00.000000Z", "2015-07-02T14:00:00.000000Z", "2015-07-02T15:00:00.000000Z", "2015-07-02T16:00:00.000000Z", "2015-07-02T17:00:00.000000Z", "2015-07-02T18:00:00.000000Z", "2015-07-02T19:00:00.000000Z", "2015-07-02T20:00:00.000000Z", "2015-07-02T21:00:00.000000Z", "2015-07-02T22:00:00.000000Z", "2015-07-02T23:00:00.000000Z", "2015-07-03T00:00:00.000000Z", "2015-07-03T01:00:00.000000Z", "2015-07-03T02:00:00.000000Z", "2015-07-03T03:00:00.000000Z", "2015-07-03T04:00:00.000000Z", "2015-07-03T05:00:00.000000Z", "2015-07-03T06:00:00.000000Z", "2015-07-03T07:00:00.000000Z", "2015-07-03T08:00:00.000000Z", "2015-07-03T09:00:00.000000Z", "2015-07-03T10:00:00.000000Z", "2015-07-03T11:00:00.000000Z", "2015-07-03T12:00:00.000000Z", "2015-07-03T13:00:00.000000Z", "2015-07-03T14:00:00.000000Z", "2015-07-03T15:00:00.000000Z", "2015-07-03T16:00:00.000000Z", "2015-07-03T17:00:00.000000Z", "2015-07-03T18:00:00.000000Z", "2015-07-03T19:00:00.000000Z", "2015-07-03T20:00:00.000000Z", "2015-07-03T21:00:00.000000Z", "2015-07-03T22:00:00.000000Z", "2015-07-03T23:00:00.000000Z", "2015-07-04T00:00:00.000000Z", "2015-07-04T01:00:00.000000Z", "2015-07-04T02:00:00.000000Z", "2015-07-04T03:00:00.000000Z", "2015-07-04T04:00:00.000000Z", "2015-07-04T05:00:00.000000Z", "2015-07-04T06:00:00.000000Z", "2015-07-04T07:00:00.000000Z", "2015-07-04T08:00:00.000000Z", "2015-07-04T09:00:00.000000Z", "2015-07-04T10:00:00.000000Z", "2015-07-04T11:00:00.000000Z", "2015-07-04T12:00:00.000000Z", "2015-07-04T13:00:00.000000Z", "2015-07-04T14:00:00.000000Z", "2015-07-04T15:00:00.000000Z", "2015-07-04T16:00:00.000000Z", "2015-07-04T17:00:00.000000Z", "2015-07-04T18:00:00.000000Z", "2015-07-04T19:00:00.000000Z", "2015-07-04T20:00:00.000000Z", "2015-07-04T21:00:00.000000Z", "2015-07-04T22:00:00.000000Z", "2015-07-04T23:00:00.000000Z", "2015-07-05T00:00:00.000000Z", "2015-07-05T01:00:00.000000Z", "2015-07-05T02:00:00.000000Z", "2015-07-05T03:00:00.000000Z", "2015-07-05T04:00:00.000000Z", "2015-07-05T05:00:00.000000Z", "2015-07-05T06:00:00.000000Z", "2015-07-05T07:00:00.000000Z", "2015-07-05T08:00:00.000000Z", "2015-07-05T09:00:00.000000Z", "2015-07-05T10:00:00.000000Z", "2015-07-05T11:00:00.000000Z", "2015-07-05T12:00:00.000000Z", "2015-07-05T13:00:00.000000Z", "2015-07-05T14:00:00.000000Z", "2015-07-05T15:00:00.000000Z", "2015-07-05T16:00:00.000000Z", "2015-07-05T17:00:00.000000Z", "2015-07-05T18:00:00.000000Z", "2015-07-05T19:00:00.000000Z", "2015-07-05T20:00:00.000000Z", "2015-07-05T21:00:00.000000Z", "2015-07-05T22:00:00.000000Z", "2015-07-05T23:00:00.000000Z", "2015-07-06T00:00:00.000000Z", "2015-07-06T01:00:00.000000Z", "2015-07-06T02:00:00.000000Z", "2015-07-06T03:00:00.000000Z", "2015-07-06T04:00:00.000000Z", "2015-07-06T05:00:00.000000Z", "2015-07-06T06:00:00.000000Z", "2015-07-06T07:00:00.000000Z", "2015-07-06T08:00:00.000000Z", "2015-07-06T09:00:00.000000Z", "2015-07-06T10:00:00.000000Z", "2015-07-06T11:00:00.000000Z", "2015-07-06T12:00:00.000000Z", "2015-07-06T13:00:00.000000Z", "2015-07-06T14:00:00.000000Z", "2015-07-06T15:00:00.000000Z", "2015-07-06T16:00:00.000000Z", "2015-07-06T17:00:00.000000Z", "2015-07-06T18:00:00.000000Z", "2015-07-06T19:00:00.000000Z", "2015-07-06T20:00:00.000000Z", "2015-07-06T21:00:00.000000Z", "2015-07-06T22:00:00.000000Z", "2015-07-06T23:00:00.000000Z", "2015-07-07T00:00:00.000000Z", "2015-07-07T01:00:00.000000Z", "2015-07-07T02:00:00.000000Z", "2015-07-07T03:00:00.000000Z", "2015-07-07T04:00:00.000000Z", "2015-07-07T05:00:00.000000Z", "2015-07-07T06:00:00.000000Z", "2015-07-07T07:00:00.000000Z", "2015-07-07T08:00:00.000000Z", "2015-07-07T09:00:00.000000Z", "2015-07-07T10:00:00.000000Z", "2015-07-07T11:00:00.000000Z", "2015-07-07T12:00:00.000000Z", "2015-07-07T13:00:00.000000Z", "2015-07-07T14:00:00.000000Z", "2015-07-07T15:00:00.000000Z", "2015-07-07T16:00:00.000000Z", "2015-07-07T17:00:00.000000Z", "2015-07-07T18:00:00.000000Z", "2015-07-07T19:00:00.000000Z", "2015-07-07T20:00:00.000000Z", "2015-07-07T21:00:00.000000Z", "2015-07-07T22:00:00.000000Z", "2015-07-07T23:00:00.000000Z", "2015-07-08T00:00:00.000000Z", "2015-07-08T01:00:00.000000Z", "2015-07-08T02:00:00.000000Z", "2015-07-08T03:00:00.000000Z", "2015-07-08T04:00:00.000000Z", "2015-07-08T05:00:00.000000Z", "2015-07-08T06:00:00.000000Z", "2015-07-08T07:00:00.000000Z", "2015-07-08T08:00:00.000000Z", "2015-07-08T09:00:00.000000Z", "2015-07-08T10:00:00.000000Z", "2015-07-08T11:00:00.000000Z", "2015-07-08T12:00:00.000000Z", "2015-07-08T13:00:00.000000Z", "2015-07-08T14:00:00.000000Z", "2015-07-08T15:00:00.000000Z", "2015-07-08T16:00:00.000000Z", "2015-07-08T17:00:00.000000Z", "2015-07-08T18:00:00.000000Z", "2015-07-08T19:00:00.000000Z", "2015-07-08T20:00:00.000000Z", "2015-07-08T21:00:00.000000Z", "2015-07-08T22:00:00.000000Z", "2015-07-08T23:00:00.000000Z", "2015-07-09T00:00:00.000000Z", "2015-07-09T01:00:00.000000Z", "2015-07-09T02:00:00.000000Z", "2015-07-09T03:00:00.000000Z", "2015-07-09T04:00:00.000000Z", "2015-07-09T05:00:00.000000Z", "2015-07-09T06:00:00.000000Z", "2015-07-09T07:00:00.000000Z", "2015-07-09T08:00:00.000000Z", "2015-07-09T09:00:00.000000Z", "2015-07-09T10:00:00.000000Z", "2015-07-09T11:00:00.000000Z", "2015-07-09T12:00:00.000000Z", "2015-07-09T13:00:00.000000Z", "2015-07-09T14:00:00.000000Z", "2015-07-09T15:00:00.000000Z", "2015-07-09T16:00:00.000000Z", "2015-07-09T17:00:00.000000Z", "2015-07-09T18:00:00.000000Z", "2015-07-09T19:00:00.000000Z", "2015-07-09T20:00:00.000000Z", "2015-07-09T21:00:00.000000Z", "2015-07-09T22:00:00.000000Z", "2015-07-09T23:00:00.000000Z", "2015-07-10T00:00:00.000000Z", "2015-07-10T01:00:00.000000Z", "2015-07-10T02:00:00.000000Z", "2015-07-10T03:00:00.000000Z", "2015-07-10T04:00:00.000000Z", "2015-07-10T05:00:00.000000Z", "2015-07-10T06:00:00.000000Z", "2015-07-10T07:00:00.000000Z", "2015-07-10T08:00:00.000000Z", "2015-07-10T09:00:00.000000Z", "2015-07-10T10:00:00.000000Z", "2015-07-10T11:00:00.000000Z", "2015-07-10T12:00:00.000000Z", "2015-07-10T13:00:00.000000Z", "2015-07-10T14:00:00.000000Z", "2015-07-10T15:00:00.000000Z", "2015-07-10T16:00:00.000000Z", "2015-07-10T17:00:00.000000Z", "2015-07-10T18:00:00.000000Z", "2015-07-10T19:00:00.000000Z", "2015-07-10T20:00:00.000000Z", "2015-07-10T21:00:00.000000Z", "2015-07-10T22:00:00.000000Z", "2015-07-10T23:00:00.000000Z", "2015-07-11T00:00:00.000000Z", "2015-07-11T01:00:00.000000Z", "2015-07-11T02:00:00.000000Z", "2015-07-11T03:00:00.000000Z", "2015-07-11T04:00:00.000000Z", "2015-07-11T05:00:00.000000Z", "2015-07-11T06:00:00.000000Z", "2015-07-11T07:00:00.000000Z", "2015-07-11T08:00:00.000000Z", "2015-07-11T09:00:00.000000Z", "2015-07-11T10:00:00.000000Z", "2015-07-11T11:00:00.000000Z", "2015-07-11T12:00:00.000000Z", "2015-07-11T13:00:00.000000Z", "2015-07-11T14:00:00.000000Z", "2015-07-11T15:00:00.000000Z", "2015-07-11T16:00:00.000000Z", "2015-07-11T17:00:00.000000Z", "2015-07-11T18:00:00.000000Z", "2015-07-11T19:00:00.000000Z", "2015-07-11T20:00:00.000000Z", "2015-07-11T21:00:00.000000Z", "2015-07-11T22:00:00.000000Z", "2015-07-11T23:00:00.000000Z", "2015-07-12T00:00:00.000000Z", "2015-07-12T01:00:00.000000Z", "2015-07-12T02:00:00.000000Z", "2015-07-12T03:00:00.000000Z", "2015-07-12T04:00:00.000000Z", "2015-07-12T05:00:00.000000Z", "2015-07-12T06:00:00.000000Z", "2015-07-12T07:00:00.000000Z", "2015-07-12T08:00:00.000000Z", "2015-07-12T09:00:00.000000Z", "2015-07-12T10:00:00.000000Z", "2015-07-12T11:00:00.000000Z", "2015-07-12T12:00:00.000000Z", "2015-07-12T13:00:00.000000Z", "2015-07-12T14:00:00.000000Z", "2015-07-12T15:00:00.000000Z", "2015-07-12T16:00:00.000000Z", "2015-07-12T17:00:00.000000Z", "2015-07-12T18:00:00.000000Z", "2015-07-12T19:00:00.000000Z", "2015-07-12T20:00:00.000000Z", "2015-07-12T21:00:00.000000Z", "2015-07-12T22:00:00.000000Z", "2015-07-12T23:00:00.000000Z", "2015-07-13T00:00:00.000000Z", "2015-07-13T01:00:00.000000Z", "2015-07-13T02:00:00.000000Z", "2015-07-13T03:00:00.000000Z", "2015-07-13T04:00:00.000000Z", "2015-07-13T05:00:00.000000Z", "2015-07-13T06:00:00.000000Z", "2015-07-13T07:00:00.000000Z", "2015-07-13T08:00:00.000000Z", "2015-07-13T09:00:00.000000Z", "2015-07-13T10:00:00.000000Z", "2015-07-13T11:00:00.000000Z", "2015-07-13T12:00:00.000000Z", "2015-07-13T13:00:00.000000Z", "2015-07-13T14:00:00.000000Z", "2015-07-13T15:00:00.000000Z", "2015-07-13T16:00:00.000000Z", "2015-07-13T17:00:00.000000Z", "2015-07-13T18:00:00.000000Z", "2015-07-13T19:00:00.000000Z", "2015-07-13T20:00:00.000000Z", "2015-07-13T21:00:00.000000Z", "2015-07-13T22:00:00.000000Z", "2015-07-13T23:00:00.000000Z", "2015-07-14T00:00:00.000000Z", "2015-07-14T01:00:00.000000Z", "2015-07-14T02:00:00.000000Z", "2015-07-14T03:00:00.000000Z", "2015-07-14T04:00:00.000000Z", "2015-07-14T05:00:00.000000Z", "2015-07-14T06:00:00.000000Z", "2015-07-14T07:00:00.000000Z", "2015-07-14T08:00:00.000000Z", "2015-07-14T09:00:00.000000Z", "2015-07-14T10:00:00.000000Z", "2015-07-14T11:00:00.000000Z", "2015-07-14T12:00:00.000000Z", "2015-07-14T13:00:00.000000Z", "2015-07-14T14:00:00.000000Z", "2015-07-14T15:00:00.000000Z", "2015-07-14T16:00:00.000000Z", "2015-07-14T17:00:00.000000Z", "2015-07-14T18:00:00.000000Z", "2015-07-14T19:00:00.000000Z", "2015-07-14T20:00:00.000000Z", "2015-07-14T21:00:00.000000Z", "2015-07-14T22:00:00.000000Z", "2015-07-14T23:00:00.000000Z", "2015-07-15T00:00:00.000000Z", "2015-07-15T01:00:00.000000Z", "2015-07-15T02:00:00.000000Z", "2015-07-15T03:00:00.000000Z", "2015-07-15T04:00:00.000000Z", "2015-07-15T05:00:00.000000Z", "2015-07-15T06:00:00.000000Z", "2015-07-15T07:00:00.000000Z", "2015-07-15T08:00:00.000000Z", "2015-07-15T09:00:00.000000Z", "2015-07-15T10:00:00.000000Z", "2015-07-15T11:00:00.000000Z", "2015-07-15T12:00:00.000000Z", "2015-07-15T13:00:00.000000Z", "2015-07-15T14:00:00.000000Z", "2015-07-15T15:00:00.000000Z", "2015-07-15T16:00:00.000000Z", "2015-07-15T17:00:00.000000Z", "2015-07-15T18:00:00.000000Z", "2015-07-15T19:00:00.000000Z", "2015-07-15T20:00:00.000000Z", "2015-07-15T21:00:00.000000Z", "2015-07-15T22:00:00.000000Z", "2015-07-15T23:00:00.000000Z", "2015-07-16T00:00:00.000000Z", "2015-07-16T01:00:00.000000Z", "2015-07-16T02:00:00.000000Z", "2015-07-16T03:00:00.000000Z", "2015-07-16T04:00:00.000000Z", "2015-07-16T05:00:00.000000Z", "2015-07-16T06:00:00.000000Z", "2015-07-16T07:00:00.000000Z", "2015-07-16T08:00:00.000000Z", "2015-07-16T09:00:00.000000Z", "2015-07-16T10:00:00.000000Z", "2015-07-16T11:00:00.000000Z", "2015-07-16T12:00:00.000000Z", "2015-07-16T13:00:00.000000Z", "2015-07-16T14:00:00.000000Z", "2015-07-16T15:00:00.000000Z", "2015-07-16T16:00:00.000000Z", "2015-07-16T17:00:00.000000Z", "2015-07-16T18:00:00.000000Z", "2015-07-16T19:00:00.000000Z", "2015-07-16T20:00:00.000000Z", "2015-07-16T21:00:00.000000Z", "2015-07-16T22:00:00.000000Z", "2015-07-16T23:00:00.000000Z", "2015-07-17T00:00:00.000000Z", "2015-07-17T01:00:00.000000Z", "2015-07-17T02:00:00.000000Z", "2015-07-17T03:00:00.000000Z", "2015-07-17T04:00:00.000000Z", "2015-07-17T05:00:00.000000Z", "2015-07-17T06:00:00.000000Z", "2015-07-17T07:00:00.000000Z", "2015-07-17T08:00:00.000000Z", "2015-07-17T09:00:00.000000Z", "2015-07-17T10:00:00.000000Z", "2015-07-17T11:00:00.000000Z", "2015-07-17T12:00:00.000000Z", "2015-07-17T13:00:00.000000Z", "2015-07-17T14:00:00.000000Z", "2015-07-17T15:00:00.000000Z", "2015-07-17T16:00:00.000000Z", "2015-07-17T17:00:00.000000Z", "2015-07-17T18:00:00.000000Z", "2015-07-17T19:00:00.000000Z", "2015-07-17T20:00:00.000000Z", "2015-07-17T21:00:00.000000Z", "2015-07-17T22:00:00.000000Z", "2015-07-17T23:00:00.000000Z", "2015-07-18T00:00:00.000000Z", "2015-07-18T01:00:00.000000Z", "2015-07-18T02:00:00.000000Z", "2015-07-18T03:00:00.000000Z", "2015-07-18T04:00:00.000000Z", "2015-07-18T05:00:00.000000Z", "2015-07-18T06:00:00.000000Z", "2015-07-18T07:00:00.000000Z", "2015-07-18T08:00:00.000000Z", "2015-07-18T09:00:00.000000Z", "2015-07-18T10:00:00.000000Z", "2015-07-18T11:00:00.000000Z", "2015-07-18T12:00:00.000000Z", "2015-07-18T13:00:00.000000Z", "2015-07-18T14:00:00.000000Z", "2015-07-18T15:00:00.000000Z", "2015-07-18T16:00:00.000000Z", "2015-07-18T17:00:00.000000Z", "2015-07-18T18:00:00.000000Z", "2015-07-18T19:00:00.000000Z", "2015-07-18T20:00:00.000000Z", "2015-07-18T21:00:00.000000Z", "2015-07-18T22:00:00.000000Z", "2015-07-18T23:00:00.000000Z", "2015-07-19T00:00:00.000000Z", "2015-07-19T01:00:00.000000Z", "2015-07-19T02:00:00.000000Z", "2015-07-19T03:00:00.000000Z", "2015-07-19T04:00:00.000000Z", "2015-07-19T05:00:00.000000Z", "2015-07-19T06:00:00.000000Z", "2015-07-19T07:00:00.000000Z", "2015-07-19T08:00:00.000000Z", "2015-07-19T09:00:00.000000Z", "2015-07-19T10:00:00.000000Z", "2015-07-19T11:00:00.000000Z", "2015-07-19T12:00:00.000000Z", "2015-07-19T13:00:00.000000Z", "2015-07-19T14:00:00.000000Z", "2015-07-19T15:00:00.000000Z", "2015-07-19T16:00:00.000000Z", "2015-07-19T17:00:00.000000Z", "2015-07-19T18:00:00.000000Z", "2015-07-19T19:00:00.000000Z", "2015-07-19T20:00:00.000000Z", "2015-07-19T21:00:00.000000Z", "2015-07-19T22:00:00.000000Z", "2015-07-19T23:00:00.000000Z", "2015-07-20T00:00:00.000000Z", "2015-07-20T01:00:00.000000Z", "2015-07-20T02:00:00.000000Z", "2015-07-20T03:00:00.000000Z", "2015-07-20T04:00:00.000000Z", "2015-07-20T05:00:00.000000Z", "2015-07-20T06:00:00.000000Z", "2015-07-20T07:00:00.000000Z", "2015-07-20T08:00:00.000000Z", "2015-07-20T09:00:00.000000Z", "2015-07-20T10:00:00.000000Z", "2015-07-20T11:00:00.000000Z", "2015-07-20T12:00:00.000000Z", "2015-07-20T13:00:00.000000Z", "2015-07-20T14:00:00.000000Z", "2015-07-20T15:00:00.000000Z", "2015-07-20T16:00:00.000000Z", "2015-07-20T17:00:00.000000Z", "2015-07-20T18:00:00.000000Z", "2015-07-20T19:00:00.000000Z", "2015-07-20T20:00:00.000000Z", "2015-07-20T21:00:00.000000Z", "2015-07-20T22:00:00.000000Z", "2015-07-20T23:00:00.000000Z", "2015-07-21T00:00:00.000000Z", "2015-07-21T01:00:00.000000Z", "2015-07-21T02:00:00.000000Z", "2015-07-21T03:00:00.000000Z", "2015-07-21T04:00:00.000000Z", "2015-07-21T05:00:00.000000Z", "2015-07-21T06:00:00.000000Z", "2015-07-21T07:00:00.000000Z", "2015-07-21T08:00:00.000000Z", "2015-07-21T09:00:00.000000Z", "2015-07-21T10:00:00.000000Z", "2015-07-21T11:00:00.000000Z", "2015-07-21T12:00:00.000000Z", "2015-07-21T13:00:00.000000Z", "2015-07-21T14:00:00.000000Z", "2015-07-21T15:00:00.000000Z", "2015-07-21T16:00:00.000000Z", "2015-07-21T17:00:00.000000Z", "2015-07-21T18:00:00.000000Z", "2015-07-21T19:00:00.000000Z", "2015-07-21T20:00:00.000000Z", "2015-07-21T21:00:00.000000Z", "2015-07-21T22:00:00.000000Z", "2015-07-21T23:00:00.000000Z", "2015-07-22T00:00:00.000000Z", "2015-07-22T01:00:00.000000Z", "2015-07-22T02:00:00.000000Z", "2015-07-22T03:00:00.000000Z", "2015-07-22T04:00:00.000000Z", "2015-07-22T05:00:00.000000Z", "2015-07-22T06:00:00.000000Z", "2015-07-22T07:00:00.000000Z", "2015-07-22T08:00:00.000000Z", "2015-07-22T09:00:00.000000Z", "2015-07-22T10:00:00.000000Z", "2015-07-22T11:00:00.000000Z", "2015-07-22T12:00:00.000000Z", "2015-07-22T13:00:00.000000Z", "2015-07-22T14:00:00.000000Z", "2015-07-22T15:00:00.000000Z", "2015-07-22T16:00:00.000000Z", "2015-07-22T17:00:00.000000Z", "2015-07-22T18:00:00.000000Z", "2015-07-22T19:00:00.000000Z", "2015-07-22T20:00:00.000000Z", "2015-07-22T21:00:00.000000Z", "2015-07-22T22:00:00.000000Z", "2015-07-22T23:00:00.000000Z", "2015-07-23T00:00:00.000000Z", "2015-07-23T01:00:00.000000Z", "2015-07-23T02:00:00.000000Z", "2015-07-23T03:00:00.000000Z", "2015-07-23T04:00:00.000000Z", "2015-07-23T05:00:00.000000Z", "2015-07-23T06:00:00.000000Z", "2015-07-23T07:00:00.000000Z", "2015-07-23T08:00:00.000000Z", "2015-07-23T09:00:00.000000Z", "2015-07-23T10:00:00.000000Z", "2015-07-23T11:00:00.000000Z", "2015-07-23T12:00:00.000000Z", "2015-07-23T13:00:00.000000Z", "2015-07-23T14:00:00.000000Z", "2015-07-23T15:00:00.000000Z", "2015-07-23T16:00:00.000000Z", "2015-07-23T17:00:00.000000Z", "2015-07-23T18:00:00.000000Z", "2015-07-23T19:00:00.000000Z", "2015-07-23T20:00:00.000000Z", "2015-07-23T21:00:00.000000Z", "2015-07-23T22:00:00.000000Z", "2015-07-23T23:00:00.000000Z", "2015-07-24T00:00:00.000000Z", "2015-07-24T01:00:00.000000Z", "2015-07-24T02:00:00.000000Z", "2015-07-24T03:00:00.000000Z", "2015-07-24T04:00:00.000000Z", "2015-07-24T05:00:00.000000Z", "2015-07-24T06:00:00.000000Z", "2015-07-24T07:00:00.000000Z", "2015-07-24T08:00:00.000000Z", "2015-07-24T09:00:00.000000Z", "2015-07-24T10:00:00.000000Z", "2015-07-24T11:00:00.000000Z", "2015-07-24T12:00:00.000000Z", "2015-07-24T13:00:00.000000Z", "2015-07-24T14:00:00.000000Z", "2015-07-24T15:00:00.000000Z", "2015-07-24T16:00:00.000000Z", "2015-07-24T17:00:00.000000Z", "2015-07-24T18:00:00.000000Z", "2015-07-24T19:00:00.000000Z", "2015-07-24T20:00:00.000000Z", "2015-07-24T21:00:00.000000Z", "2015-07-24T22:00:00.000000Z", "2015-07-24T23:00:00.000000Z", "2015-07-25T00:00:00.000000Z", "2015-07-25T01:00:00.000000Z", "2015-07-25T02:00:00.000000Z", "2015-07-25T03:00:00.000000Z", "2015-07-25T04:00:00.000000Z", "2015-07-25T05:00:00.000000Z", "2015-07-25T06:00:00.000000Z", "2015-07-25T07:00:00.000000Z", "2015-07-25T08:00:00.000000Z", "2015-07-25T09:00:00.000000Z", "2015-07-25T10:00:00.000000Z", "2015-07-25T11:00:00.000000Z", "2015-07-25T12:00:00.000000Z", "2015-07-25T13:00:00.000000Z", "2015-07-25T14:00:00.000000Z", "2015-07-25T15:00:00.000000Z", "2015-07-25T16:00:00.000000Z", "2015-07-25T17:00:00.000000Z", "2015-07-25T18:00:00.000000Z", "2015-07-25T19:00:00.000000Z", "2015-07-25T20:00:00.000000Z", "2015-07-25T21:00:00.000000Z", "2015-07-25T22:00:00.000000Z", "2015-07-25T23:00:00.000000Z", "2015-07-26T00:00:00.000000Z", "2015-07-26T01:00:00.000000Z", "2015-07-26T02:00:00.000000Z", "2015-07-26T03:00:00.000000Z", "2015-07-26T04:00:00.000000Z", "2015-07-26T05:00:00.000000Z", "2015-07-26T06:00:00.000000Z", "2015-07-26T07:00:00.000000Z", "2015-07-26T08:00:00.000000Z", "2015-07-26T09:00:00.000000Z", "2015-07-26T10:00:00.000000Z", "2015-07-26T11:00:00.000000Z", "2015-07-26T12:00:00.000000Z", "2015-07-26T13:00:00.000000Z", "2015-07-26T14:00:00.000000Z", "2015-07-26T15:00:00.000000Z", "2015-07-26T16:00:00.000000Z", "2015-07-26T17:00:00.000000Z", "2015-07-26T18:00:00.000000Z", "2015-07-26T19:00:00.000000Z", "2015-07-26T20:00:00.000000Z", "2015-07-26T21:00:00.000000Z", "2015-07-26T22:00:00.000000Z", "2015-07-26T23:00:00.000000Z", "2015-07-27T00:00:00.000000Z", "2015-07-27T01:00:00.000000Z", "2015-07-27T02:00:00.000000Z", "2015-07-27T03:00:00.000000Z", "2015-07-27T04:00:00.000000Z", "2015-07-27T05:00:00.000000Z", "2015-07-27T06:00:00.000000Z", "2015-07-27T07:00:00.000000Z", "2015-07-27T08:00:00.000000Z", "2015-07-27T09:00:00.000000Z", "2015-07-27T10:00:00.000000Z", "2015-07-27T11:00:00.000000Z", "2015-07-27T12:00:00.000000Z", "2015-07-27T13:00:00.000000Z", "2015-07-27T14:00:00.000000Z", "2015-07-27T15:00:00.000000Z", "2015-07-27T16:00:00.000000Z", "2015-07-27T17:00:00.000000Z", "2015-07-27T18:00:00.000000Z", "2015-07-27T19:00:00.000000Z", "2015-07-27T20:00:00.000000Z", "2015-07-27T21:00:00.000000Z", "2015-07-27T22:00:00.000000Z", "2015-07-27T23:00:00.000000Z", "2015-07-28T00:00:00.000000Z", "2015-07-28T01:00:00.000000Z", "2015-07-28T02:00:00.000000Z", "2015-07-28T03:00:00.000000Z", "2015-07-28T04:00:00.000000Z", "2015-07-28T05:00:00.000000Z", "2015-07-28T06:00:00.000000Z", "2015-07-28T07:00:00.000000Z", "2015-07-28T08:00:00.000000Z", "2015-07-28T09:00:00.000000Z", "2015-07-28T10:00:00.000000Z", "2015-07-28T11:00:00.000000Z", "2015-07-28T12:00:00.000000Z", "2015-07-28T13:00:00.000000Z", "2015-07-28T14:00:00.000000Z", "2015-07-28T15:00:00.000000Z", "2015-07-28T16:00:00.000000Z", "2015-07-28T17:00:00.000000Z", "2015-07-28T18:00:00.000000Z", "2015-07-28T19:00:00.000000Z", "2015-07-28T20:00:00.000000Z", "2015-07-28T21:00:00.000000Z", "2015-07-28T22:00:00.000000Z", "2015-07-28T23:00:00.000000Z", "2015-07-29T00:00:00.000000Z", "2015-07-29T01:00:00.000000Z", "2015-07-29T02:00:00.000000Z", "2015-07-29T03:00:00.000000Z", "2015-07-29T04:00:00.000000Z", "2015-07-29T05:00:00.000000Z", "2015-07-29T06:00:00.000000Z", "2015-07-29T07:00:00.000000Z", "2015-07-29T08:00:00.000000Z", "2015-07-29T09:00:00.000000Z", "2015-07-29T10:00:00.000000Z", "2015-07-29T11:00:00.000000Z", "2015-07-29T12:00:00.000000Z", "2015-07-29T13:00:00.000000Z", "2015-07-29T14:00:00.000000Z", "2015-07-29T15:00:00.000000Z", "2015-07-29T16:00:00.000000Z", "2015-07-29T17:00:00.000000Z", "2015-07-29T18:00:00.000000Z", "2015-07-29T19:00:00.000000Z", "2015-07-29T20:00:00.000000Z", "2015-07-29T21:00:00.000000Z", "2015-07-29T22:00:00.000000Z", "2015-07-29T23:00:00.000000Z", "2015-07-30T00:00:00.000000Z", "2015-07-30T01:00:00.000000Z", "2015-07-30T02:00:00.000000Z", "2015-07-30T03:00:00.000000Z", "2015-07-30T04:00:00.000000Z", "2015-07-30T05:00:00.000000Z", "2015-07-30T06:00:00.000000Z", "2015-07-30T07:00:00.000000Z", "2015-07-30T08:00:00.000000Z", "2015-07-30T09:00:00.000000Z", "2015-07-30T10:00:00.000000Z", "2015-07-30T11:00:00.000000Z", "2015-07-30T12:00:00.000000Z", "2015-07-30T13:00:00.000000Z", "2015-07-30T14:00:00.000000Z", "2015-07-30T15:00:00.000000Z", "2015-07-30T16:00:00.000000Z", "2015-07-30T17:00:00.000000Z", "2015-07-30T18:00:00.000000Z", "2015-07-30T19:00:00.000000Z", "2015-07-30T20:00:00.000000Z", "2015-07-30T21:00:00.000000Z", "2015-07-30T22:00:00.000000Z", "2015-07-30T23:00:00.000000Z", "2015-07-31T00:00:00.000000Z", "2015-07-31T01:00:00.000000Z", "2015-07-31T02:00:00.000000Z", "2015-07-31T03:00:00.000000Z", "2015-07-31T04:00:00.000000Z", "2015-07-31T05:00:00.000000Z", "2015-07-31T06:00:00.000000Z", "2015-07-31T07:00:00.000000Z", "2015-07-31T08:00:00.000000Z", "2015-07-31T09:00:00.000000Z", "2015-07-31T10:00:00.000000Z", "2015-07-31T11:00:00.000000Z", "2015-07-31T12:00:00.000000Z", "2015-07-31T13:00:00.000000Z", "2015-07-31T14:00:00.000000Z", "2015-07-31T15:00:00.000000Z", "2015-07-31T16:00:00.000000Z", "2015-07-31T17:00:00.000000Z", "2015-07-31T18:00:00.000000Z", "2015-07-31T19:00:00.000000Z", "2015-07-31T20:00:00.000000Z", "2015-07-31T21:00:00.000000Z", "2015-07-31T22:00:00.000000Z", "2015-07-31T23:00:00.000000Z", "2015-08-01T00:00:00.000000Z", "2015-08-01T01:00:00.000000Z", "2015-08-01T02:00:00.000000Z", "2015-08-01T03:00:00.000000Z", "2015-08-01T04:00:00.000000Z", "2015-08-01T05:00:00.000000Z", "2015-08-01T06:00:00.000000Z", "2015-08-01T07:00:00.000000Z", "2015-08-01T08:00:00.000000Z", "2015-08-01T09:00:00.000000Z", "2015-08-01T10:00:00.000000Z", "2015-08-01T11:00:00.000000Z", "2015-08-01T12:00:00.000000Z", "2015-08-01T13:00:00.000000Z", "2015-08-01T14:00:00.000000Z", "2015-08-01T15:00:00.000000Z"], "xaxis": "x", "y": [3124, 2990, 2862, 2809, 2544, 2201, 1996, 1861, 1735, 1713, 1724, 1798, 1891, 2037, 2102, 2163, 2269, 2404, 2515, 2621, 2745, 2816, 2938, 3022, 2976, 2892, 2784, 2725, 2530, 2211, 1995, 1833, 1768, 1712, 1707, 1762, 1880, 1995, 2134, 2227, 2376, 2477, 2597, 2691, 2751, 2782, 2810, 2781, 2693, 2567, 2490, 2448, 2277, 1997, 1785, 1689, 1562, 1560, 1505, 1538, 1641, 1735, 1950, 2138, 2303, 2432, 2528, 2656, 2740, 2803, 2855, 2880, 2778, 2637, 2479, 2381, 2228, 2037, 1758, 1648, 1560, 1508, 1486, 1486, 1515, 1623, 1919, 2172, 2416, 2605, 2755, 2822, 2917, 2997, 3060, 3046, 2942, 2758, 2487, 2349, 2299, 2132, 1950, 1783, 1768, 1569, 1581, 1560, 1562, 1749, 2005, 2269, 2488, 2717, 2919, 3110, 3158, 3264, 3301, 3337, 3247, 3079, 2935, 2833, 2598, 2280, 2041, 1903, 1735, 1680, 1721, 1805, 1908, 2129, 2249, 2449, 2625, 2819, 2939, 3090, 3161, 3220, 3270, 3285, 3218, 3038, 2961, 2843, 2603, 2263, 2070, 1937, 1869, 1774, 1808, 1847, 1985, 2061, 2096, 2160, 2257, 2248, 2207, 2221, 2224, 2226, 2225, 2253, 2243, 2212, 2221, 2187, 2043, 1778, 1653, 1562, 1490, 1477, 1503, 1609, 1722, 1850, 1980, 2019, 2094, 2151, 2150, 2191, 2188, 2202, 2266, 2304, 2304, 2228, 2198, 2179, 2014, 1792, 1656, 1575, 1505, 1509, 1466, 1571, 1689, 1838, 1903, 1968, 2044, 2149, 2233, 2308, 2353, 2413, 2439, 2457, 2444, 2380, 2331, 2311, 2096, 1870, 1709, 1584, 1529, 1489, 1489, 1606, 1723, 1872, 1999, 2122, 2262, 2468, 2620, 2771, 2889, 2958, 3016, 3045, 2980, 2822, 2723, 2667, 2463, 2177, 1976, 1835, 1724, 1663, 1671, 1661, 1710, 1873, 2153, 2446, 2709, 2898, 3076, 3239, 3344, 3471, 3516, 3515, 3448, 3277, 3100, 2992, 2772, 2434, 2184, 2010, 1846, 1768, 1687, 1700, 1742, 1929, 2304, 2643, 2869, 3065, 3271, 3434, 3551, 3614, 3667, 3716, 3670, 3508, 3341, 3232, 2908, 2576, 2319, 2161, 2018, 1954, 1923, 1998, 2139, 2285, 2521, 2767, 3053, 3281, 3474, 3653, 3762, 3839, 3883, 3915, 3866, 3724, 3544, 3435, 3103, 2769, 2514, 2319, 2179, 2118, 2076, 2148, 2212, 2394, 2593, 2840, 3097, 3271, 3331, 3375, 3419, 3528, 3570, 3620, 3621, 3516, 3320, 3219, 2885, 2543, 2265, 2115, 1970, 1906, 1855, 1973, 2039, 2243, 2430, 2634, 2864, 3093, 3270, 3410, 3458, 3496, 3501, 3525, 3493, 3325, 3162, 3046, 2756, 2463, 2222, 2036, 1907, 1868, 1832, 1919, 2016, 2179, 2392, 2606, 2908, 3109, 3299, 3476, 3597, 3667, 3769, 3784, 3719, 3527, 3354, 3264, 2973, 2652, 2389, 2210, 2145, 2007, 2031, 2064, 2181, 2378, 2614, 2845, 3072, 3305, 3516, 3609, 3786, 3767, 3792, 3783, 3731, 3550, 3396, 3288, 3034, 2712, 2497, 2324, 2208, 2110, 2007, 2100, 2052, 2278, 2523, 2739, 2938, 3099, 3256, 3398, 3486, 3601, 3697, 3716, 3668, 3520, 3348, 3235, 2942, 2669, 2404, 2227, 2108, 1998, 1985, 1975, 1972, 2120, 2384, 2607, 2769, 2949, 3138, 3311, 3422, 3532, 3575, 3637, 3599, 3496, 3342, 3196, 2894, 2538, 2322, 2187, 2030, 1957, 1976, 2025, 2133, 2253, 2381, 2541, 2649, 2864, 3044, 3202, 3346, 3430, 3472, 3478, 3425, 3244, 3086, 2938, 2666, 2342, 2132, 1978, 1901, 1836, 1815, 1872, 1976, 2087, 2166, 2311, 2436, 2585, 2688, 2801, 2852, 2908, 2925, 2951, 2904, 2769, 2671, 2609, 2364, 2100, 1910, 1756, 1678, 1638, 1652, 1735, 1838, 1972, 2051, 2192, 2356, 2504, 2629, 2756, 2855, 2927, 2972, 3018, 2986, 2881, 2817, 2718, 2504, 2248, 2062, 1905, 1793, 1808, 1788, 1885, 1985, 2091, 2170, 2322, 2505, 2706, 2886, 3070, 3205, 3330, 3423, 3497, 3439, 3286, 3114, 2993, 2741, 2425, 2188, 1988, 1875, 1794, 1767, 1853, 1903, 2119, 2314, 2553, 2817, 3084, 3313, 3507, 3625, 3698, 3771, 3802, 3699, 3492, 3293, 3098, 2824, 2548, 2278, 2083, 1970, 1873, 1819, 1795, 1845, 1949, 2193, 2434, 2686, 2961, 3195, 3388, 3519, 3623, 3703, 3727, 3639, 3495, 3317, 3211, 2962, 2682, 2426, 2240, 2140, 2017, 1927, 1941, 1923, 2076, 2330, 2635, 2809, 3018, 3219, 3420, 3569, 3655, 3733, 3769, 3699, 3536, 3363, 3258, 2933, 2601, 2367, 2175, 2031, 1937, 1956, 1998, 2112, 2287, 2524, 2779, 3150, 3403, 3627, 3791, 3815, 3903, 3901, 3872, 3794, 3642, 3474, 3333, 3008, 2675, 2414, 2241, 2125, 2011, 1978, 2030, 2138, 2313, 2561, 2867, 3133, 3436, 3634, 3782, 3864, 3995, 4000, 4013, 3963, 3806, 3636, 3451, 3077, 2710, 2449, 2278, 2114, 2049, 2043, 2043, 2197, 2342, 2523, 2754, 2980, 3203, 3362, 3519, 3624, 3692, 3698, 3705, 3652, 3460, 3248, 3105, 2771, 2396, 2189, 1984, 1872, 1843, 1828, 1872, 1952, 2069, 2228, 2419, 2640, 2850, 3001, 3129, 3244, 3295, 3322, 3322, 3286, 3092, 2936, 2825, 2519, 2197, 1997, 1775, 1725, 1630, 1644, 1722, 1808, 1961, 2154, 2355, 2605, 2821, 3018, 3206, 3356, 3499, 3550, 3558, 3488, 3224, 3054, 2918], "yaxis": "y"}], "layout": {"legend": {"tracegroupgap": 0}, "template": {"data": {"bar": [{"error_x": {"color": "#f2f5fa"}, "error_y": {"color": "#f2f5fa"}, "marker": {"line": {"color": "rgb(17,17,17)", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "rgb(17,17,17)", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#A2B1C6", "gridcolor": "#506784", "linecolor": "#506784", "minorgridcolor": "#506784", "startlinecolor": "#A2B1C6"}, "baxis": {"endlinecolor": "#A2B1C6", "gridcolor": "#506784", "linecolor": "#506784", "minorgridcolor": "#506784", "startlinecolor": "#A2B1C6"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"marker": {"line": {"color": "#283442"}}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"line": {"color": "#283442"}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#506784"}, "line": {"color": "rgb(17,17,17)"}}, "header": {"fill": {"color": "#2a3f5f"}, "line": {"color": "rgb(17,17,17)"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#f2f5fa", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#f2f5fa"}, "geo": {"bgcolor": "rgb(17,17,17)", "lakecolor": "rgb(17,17,17)", "landcolor": "rgb(17,17,17)", "showlakes": true, "showland": true, "subunitcolor": "#506784"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "dark"}, "paper_bgcolor": "rgb(17,17,17)", "plot_bgcolor": "rgb(17,17,17)", "polar": {"angularaxis": {"gridcolor": "#506784", "linecolor": "#506784", "ticks": ""}, "bgcolor": "rgb(17,17,17)", "radialaxis": {"gridcolor": "#506784", "linecolor": "#506784", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "rgb(17,17,17)", "gridcolor": "#506784", "gridwidth": 2, "linecolor": "#506784", "showbackground": true, "ticks": "", "zerolinecolor": "#C8D4E3"}, "yaxis": {"backgroundcolor": "rgb(17,17,17)", "gridcolor": "#506784", "gridwidth": 2, "linecolor": "#506784", "showbackground": true, "ticks": "", "zerolinecolor": "#C8D4E3"}, "zaxis": {"backgroundcolor": "rgb(17,17,17)", "gridcolor": "#506784", "gridwidth": 2, "linecolor": "#506784", "showbackground": true, "ticks": "", "zerolinecolor": "#C8D4E3"}}, "shapedefaults": {"line": {"color": "#f2f5fa"}}, "sliderdefaults": {"bgcolor": "#C8D4E3", "bordercolor": "rgb(17,17,17)", "borderwidth": 1, "tickwidth": 0}, "ternary": {"aaxis": {"gridcolor": "#506784", "linecolor": "#506784", "ticks": ""}, "baxis": {"gridcolor": "#506784", "linecolor": "#506784", "ticks": ""}, "bgcolor": "rgb(17,17,17)", "caxis": {"gridcolor": "#506784", "linecolor": "#506784", "ticks": ""}}, "title": {"x": 0.05}, "updatemenudefaults": {"bgcolor": "#506784", "borderwidth": 0}, "xaxis": {"automargin": true, "gridcolor": "#283442", "linecolor": "#506784", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "#283442", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "#283442", "linecolor": "#506784", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "#283442", "zerolinewidth": 2}}}, "title": {"text": "T2"}, "xaxis": {"anchor": "y", "domain": [0, 1], "showgrid": true, "tickangle": 45, "title": {"text": "Date"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "showgrid": true, "title": {"text": "Values"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["import plotly.express as px\n", "\n", "# Create a Plotly figure\n", "fig = px.line(series, x='ds', y='values', title=id, labels={'ds': 'Date', 'values': 'Values'})\n", "\n", "# Update layout for better readability\n", "fig.update_layout(\n", "    xaxis_title='Date',\n", "    yaxis_title='Values',\n", "    xaxis=dict(showgrid=True, tickangle=45),\n", "    yaxis=dict(showgrid=True),\n", "    template='plotly_dark'\n", ")\n", "\n", "# Show the figure\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Temporal Agents\n", "\n", "- StatisticalForecast\n", "- NeuralForecast\n", "- FoundationalForecast\n", "- ForecastCombination\n", "- ForecastReconciliation\n", "- CodeExecutor\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Reconciled Forecasts: [100. 200. 300.]\n", "Reconciled Covariance Matrix:\n", " [[1.  0.5 0.3]\n", " [0.5 1.  0.2]\n", " [0.3 0.2 1. ]]\n"]}], "source": ["import numpy as np\n", "\n", "class GaussianReconciliation:\n", "    def __init__(self, Sct, covariance_matrix):\n", "        \"\"\"\n", "        Initialize the Gaussian reconciliation with structural matrix Sct and covariance matrix.\n", "\n", "        Parameters:\n", "        - Sct: Cross-temporal structural matrix.\n", "        - covariance_matrix: Covariance matrix of the base forecasts.\n", "        \"\"\"\n", "        self.Sct = Sct\n", "        self.covariance_matrix = covariance_matrix\n", "        self.inv_covariance = np.linalg.inv(covariance_matrix)\n", "\n", "    def reconcile_forecasts(self, base_forecasts):\n", "        \"\"\"\n", "        Reconcile base forecasts using the projection matrix method.\n", "\n", "        Parameters:\n", "        - base_forecasts: Array of base forecasts (incoherent forecasts).\n", "\n", "        Returns:\n", "        - reconciled_forecasts: Reconciled forecasts.\n", "        \"\"\"\n", "        # Calculate the projection matrix M\n", "        Sct_cov_Sct_T_inv = np.linalg.inv(self.Sct @ self.inv_covariance @ self.Sct.T)\n", "        M = self.Sct.T @ Sct_cov_Sct_T_inv @ self.Sct @ self.inv_covariance\n", "        \n", "        # Reconcile the forecasts\n", "        reconciled_forecasts = M @ base_forecasts\n", "        return reconciled_forecasts\n", "\n", "    def reconcile_covariance(self):\n", "        \"\"\"\n", "        Compute the reconciled covariance matrix.\n", "\n", "        Returns:\n", "        - reconciled_covariance: Reconciled covariance matrix.\n", "        \"\"\"\n", "        # Calculate the projection matrix M\n", "        Sct_cov_Sct_T_inv = np.linalg.inv(self.Sct @ self.inv_covariance @ self.Sct.T)\n", "        M = self.Sct.T @ Sct_cov_Sct_T_inv @ self.Sct @ self.inv_covariance\n", "        \n", "        # Reconcile the covariance matrix\n", "        reconciled_covariance = M @ self.covariance_matrix @ M.T\n", "        return reconciled_covariance\n", "\n", "# Example Usage\n", "# Assume Sct is the structural matrix, covariance_matrix is the covariance matrix of base forecasts, \n", "# and base_forecasts is the vector of base forecasts.\n", "\n", "# Update the structural matrix to match the dimensionality of the problem\n", "Sct = np.array([[1, 0, 1], [0, 1, 1], [1, 1, 0]])  # Updated structural matrix\n", "covariance_matrix = np.array([[1.0, 0.5, 0.3], [0.5, 1.0, 0.2], [0.3, 0.2, 1.0]])  # Covariance matrix\n", "base_forecasts = np.array([100, 200, 300])  # Base forecasts\n", "\n", "# Create the reconciliation object\n", "reconciliation = GaussianReconciliation(Sct, covariance_matrix)\n", "\n", "# Get the reconciled forecasts\n", "reconciled_forecasts = reconciliation.reconcile_forecasts(base_forecasts)\n", "reconciled_covariance = reconciliation.reconcile_covariance()\n", "\n", "print(\"Reconciled Forecasts:\", reconciled_forecasts)\n", "print(\"Reconciled Covariance Matrix:\\n\", reconciled_covariance)\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1, 0, 1],\n", "       [0, 1, 1],\n", "       [1, 1, 0]])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["Sct"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([100, 200, 300])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["base_forecasts"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1. , 0.5, 0.3],\n", "       [0.5, 1. , 0.2],\n", "       [0.3, 0.2, 1. ]])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["covariance_matrix"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 2}