import pandas as pd
import streamlit as st
import plotly.express as px

# UTILITIES

# Function to visualize time series data
def plot_all_time_series(df, ds_col, id_col, measure_cols):
    df_melted = df.melt(id_vars=[ds_col, id_col], value_vars=measure_cols,
                        var_name='variable', value_name='value')
    fig = px.line(df_melted, x=ds_col, y='value', color=id_col,
                  facet_row='variable', title='Multiple Variables over Time')
    return fig

def plot_time_series(series, title):

    fig = px.line(series, x='ds', y='values', title=title, labels={'ds': 'Date', 'values': 'Values'})

    fig.update_traces(line=dict(color='orange'))

    # Update layout for better readability
    fig.update_layout(
        xaxis_title='Time',
        yaxis_title='Y',
        xaxis=dict(showgrid=True, tickangle=45),
        yaxis=dict(showgrid=True),
        template='plotly_dark'
    )

    # Show the figure
    return fig

def plot_clusters(df):
    # Plotting the results using Plotly
    fig = px.scatter(df, x='UMAP1', y='UMAP2', color='Cluster', 
                    title='Clustering in Lower Dimensions',
                    labels={'Cluster': 'Cluster'},
                    color_continuous_scale=px.colors.qualitative.Set1)

    # Show the plot
    return fig

