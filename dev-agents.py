

from commandcast.agent_manager import CodeExecutor, Programmer

# AutoGen Agentic Framework
import autogen
from autogen import ConversableAgent, AssistantA<PERSON>, UserProxyAgent, register_function
from autogen.agentchat.contrib.retrieve_user_proxy_agent import RetrieveUserProxyAgent
from autogen.agentchat.contrib.capabilities.vision_capability import VisionCapability
from autogen.agentchat.contrib.multimodal_conversable_agent import MultimodalConversableAgent
from autogen.coding import DockerCommandLineCodeExecutor

import chromadb

deault_llm_config = {
    "model": "gpt-4o-mini",
    "base_url": "https://pathfinder-openai.openai.azure.com",
    "api_type": "azure",
    "api_version": "2024-08-01-preview",
    "api_key": "76DBvV7xKnDwEyNe8iCFgBgt6H01VbywD2HulnYbQrIdlISWSYbFJQQJ99AKACMsfrFXJ3w3AAABACOGWqJF"
}

#url = 'https://pathfinder-openai.openai.azure.com/openai/deployments/gpt-4o-mini/chat/completions?api-version=2024-08-01-preview'
#key = '76DBvV7xKnDwEyNe8iCFgBgt6H01VbywD2HulnYbQrIdlISWSYbFJQQJ99AKACMsfrFXJ3w3AAABACOGWqJF'


from commandcast.data_manager import DataManager

db = DataManager(
    host='localhost',
    port=9000
)

import pandas as pd

df = pd.read_csv("data/m4.csv")
df.head()

df['ds'] = pd.to_datetime(df['ds'])
df.dtypes

# ID Columns 
ds_col = 'ds'
id_col = 'unique_id'

# Measurement Columns
measure_cols = list(df.drop(['ds', 'unique_id'], axis=1).columns)        

# Configure Dataset in Database
data_config = {
    'dataset_name' : 'M4_HOURLY', # Overall name of the timeseries dataset
    'feature_engineering': 'minimal',
    'ds_col' : ds_col, # Timeseries column
    'id_col' : id_col, # Identifier Unique to Timeseries,
    'hierarchy': [],
    'measure_cols': measure_cols
}

id = 'T2'
features = db.get_features(id, table_name = data_config['dataset_name'] + "_meta")
series = db.get_series(id, table_name = data_config['dataset_name'] + "_ts")

# The code writer agent's system message is to instruct the LLM on how to use
# the code executor in the code executor agent.
code_writer_system_message = """You are a helpful AI assistant.
Solve tasks using your coding and language skills.
In the following cases, suggest python code (in a python coding block) or shell script (in a sh coding block) for the user to execute.
1. When you need to collect info, use the code to output the info you need, for example, browse or search the web, download/read a file, print the content of a webpage or a file, get the current date/time, check the operating system. After sufficient info is printed and the task is ready to be solved based on your language skill, you can solve the task by yourself.
2. When you need to perform some task with code, use the code to perform the task and output the result. Finish the task smartly.
Solve the task step by step if you need to. If a plan is not provided, explain your plan first. Be clear which step uses code, and which step uses your language skill.
When using code, you must indicate the script type in the code block. The user cannot provide any other feedback or perform any other action beyond executing the code you suggest. The user can't modify your code. So do not suggest incomplete code which requires users to modify. Don't use a code block if it's not intended to be executed by the user.
If you want the user to save the code in a file before executing it, put # filename: <filename> inside the code block as the first line. Don't include multiple code blocks in one response. Do not ask users to copy and paste the result. Instead, use 'print' function for the output when relevant. Check the execution result returned by the user.
If the result indicates there is an error, fix the error and output the code again. Suggest the full code instead of partial code or code changes. If the error can't be fixed or if the task is not solved even after the code is executed successfully, analyze the problem, revisit your assumption, collect additional info you need, and think of a different approach to try.
When you find an answer, verify the answer carefully. Include verifiable evidence in your response if possible.
Reply 'TERMINATE' in the end when everything is done.
"""

# code_writer_agent = ConversableAgent(
#     "code_writer_agent",
#     system_message=code_writer_system_message,
#     llm_config={"config_list": [{"model": "gpt-4", "api_key": os.environ["OPENAI_API_KEY"]}]},
#     code_execution_config=False,  # Turn off code execution for this agent.
# )

message_with_code_block = """This is a message with two code blocks.

The first code block is:
```shell
pip install matplotlib numpy
```

The second code block is below:
```python
import numpy as np
import matplotlib.pyplot as plt
x = np.random.randint(0, 100, 100)
y = np.random.randint(0, 100, 100)
plt.scatter(x, y)
plt.savefig('scatter.png')
print('Scatter plot saved to scatter.png')
```
This is the end of the message.
"""

code_executor = CodeExecutor()

programmer = Programmer(
    llm_config=defdault_llm_config
)

# code_executor.initiate_chat(
#     programmer,
#     max_turns=12,
#     message="write some code to generate a plot of a sine curve"
# )

# Generate a reply for the given code.
reply = code_executor.generate_reply(messages=[{"role": "user", "content": message_with_code_block}])
print(reply)


print(code_executor.work_dir.name)

#import plotly.graph_objects as go
from PIL import Image

# Load the PNG image using PIL
image_path = f'{code_executor.work_dir.name}/scatter.png'  # Replace this with your PNG file path
image = Image.open(image_path)
image.show()

