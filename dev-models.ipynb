{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sklearn.model_selection import train_test_split, cross_val_score\n", "from sklearn.base import BaseEstimator\n", "\n", "class Experiment:\n", "    def __init__(self, X, y, test_size=0.2, random_state=None):\n", "        \"\"\"\n", "        Initializes the Experiment class with training and test data.\n", "        \n", "        Args:\n", "        - X: Features data (numpy array or pandas DataFrame).\n", "        - y: Target data (numpy array or pandas Series).\n", "        - test_size: Fraction of the dataset to be used as test data.\n", "        - random_state: Random seed for reproducibility.\n", "        \"\"\"\n", "        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(X, y, test_size=test_size, random_state=random_state)\n", "        self.models = {}\n", "        self.cv_splits = None\n", "        self.train_predictions = None\n", "        self.test_predictions = None\n", "\n", "    def add_model(self, name, model: BaseEstimator):\n", "        \"\"\"\n", "        Adds a fitted model to the registry.\n", "        \n", "        Args:\n", "        - name: Name of the model as a string.\n", "        - model: A fitted scikit-learn model (must be an instance of BaseEstimator).\n", "        \"\"\"\n", "        self.models[name] = model\n", "    \n", "    def train(self, model_name, model: BaseEstimator, cv=5):\n", "        \"\"\"\n", "        Trains the given model and performs cross-validation.\n", "        \n", "        Args:\n", "        - model_name: Name of the model as a string.\n", "        - model: A scikit-learn model (must be an instance of BaseEstimator).\n", "        - cv: Number of cross-validation folds.\n", "        \"\"\"\n", "        self.cv_splits = cross_val_score(model, self.X_train, self.y_train, cv=cv)\n", "        model.fit(self.X_train, self.y_train)\n", "        self.add_model(model_name, model)\n", "        self.train_predictions = model.predict(self.X_train)\n", "        self.test_predictions = model.predict(self.X_test)\n", "\n", "    def predict(self, model_name, X):\n", "        \"\"\"\n", "        Makes predictions using the specified model.\n", "        \n", "        Args:\n", "        - model_name: Name of the model to use for predictions.\n", "        - X: Data to make predictions on.\n", "        \n", "        Returns:\n", "        - Predicted values.\n", "        \"\"\"\n", "        model = self.models.get(model_name)\n", "        if model:\n", "            return model.predict(X)\n", "        else:\n", "            raise ValueError(f\"Model {model_name} not found in registry.\")\n", "\n", "    def reconcile(self):\n", "        \"\"\"\n", "        Prints a summary of model performances.\n", "        This could include mean CV scores and any other relevant metrics.\n", "        \"\"\"\n", "        print(\"Model Reconciliation:\")\n", "        for name, model in self.models.items():\n", "            cv_score = self.cv_splits.mean() if self.cv_splits is not None else 'N/A'\n", "            print(f\"Model: {name}, CV Score: {cv_score:.4f}\")\n", "\n", "    def visualize(self):\n", "        \"\"\"\n", "        Visualizes predictions against actual values for both training and test sets.\n", "        \"\"\"\n", "        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))\n", "        \n", "        # Visualize training predictions\n", "        ax1.scatter(self.y_train, self.train_predictions, alpha=0.7)\n", "        ax1.plot([self.y_train.min(), self.y_train.max()], \n", "                 [self.y_train.min(), self.y_train.max()], 'r--')\n", "        ax1.set_title('Training Predictions vs Actual')\n", "        ax1.set_xlabel('Actual Values')\n", "        ax1.set_ylabel('Predicted Values')\n", "\n", "        # Visualize test predictions\n", "        ax2.scatter(self.y_test, self.test_predictions, alpha=0.7)\n", "        ax2.plot([self.y_test.min(), self.y_test.max()], \n", "                 [self.y_test.min(), self.y_test.max()], 'r--')\n", "        ax2.set_title('Test Predictions vs Actual')\n", "        ax2.set_xlabel('Actual Values')\n", "        ax2.set_ylabel('Predicted Values')\n", "\n", "        plt.tight_layout()\n", "        plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Example Usage"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model Reconciliation:\n", "Model: <PERSON><PERSON><PERSON><PERSON>, CV Score: 0.4306\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from sklearn.datasets import make_regression\n", "from sklearn.ensemble import RandomForestRegressor\n", "\n", "# Create sample data\n", "X, y = make_regression(n_samples=100, n_features=10, noise=0.1)\n", "\n", "# Create Experiment instance\n", "experiment = Experiment(X, y)\n", "\n", "# Initialize a model\n", "rf_model = RandomForestRegressor()\n", "\n", "# Train the model\n", "experiment.train('RandomForest', rf_model)\n", "\n", "# Reconcile model results\n", "experiment.reconcile()\n", "\n", "# Visualize predictions\n", "experiment.visualize()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["['X_test',\n", " 'X_train',\n", " '__class__',\n", " '__delattr__',\n", " '__dict__',\n", " '__dir__',\n", " '__doc__',\n", " '__eq__',\n", " '__format__',\n", " '__ge__',\n", " '__getattribute__',\n", " '__gt__',\n", " '__hash__',\n", " '__init__',\n", " '__init_subclass__',\n", " '__le__',\n", " '__lt__',\n", " '__module__',\n", " '__ne__',\n", " '__new__',\n", " '__reduce__',\n", " '__reduce_ex__',\n", " '__repr__',\n", " '__setattr__',\n", " '__sizeof__',\n", " '__str__',\n", " '__subclasshook__',\n", " '__weakref__',\n", " 'add_model',\n", " 'cv_splits',\n", " 'models',\n", " 'predict',\n", " 'reconcile',\n", " 'test_predictions',\n", " 'train',\n", " 'train_predictions',\n", " 'visualize',\n", " 'y_test',\n", " 'y_train']"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["dir(experiment)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Time Series Utilities\n", "\n", "https://nixtlaverse.nixtla.io/utilsforecast/index.html"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["\n"]}], "metadata": {"kernelspec": {"display_name": "commandcast", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}